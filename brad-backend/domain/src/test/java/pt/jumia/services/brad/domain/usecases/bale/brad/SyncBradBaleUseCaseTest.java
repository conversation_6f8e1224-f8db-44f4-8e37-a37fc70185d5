package pt.jumia.services.brad.domain.usecases.bale.brad;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import pt.jumia.services.brad.domain.entities.Bale;
import pt.jumia.services.brad.domain.entities.Currency;
import pt.jumia.services.brad.domain.entities.FxRate;
import pt.jumia.services.brad.domain.entities.account.Account;
import pt.jumia.services.brad.domain.entities.fake.FakeBale;
import pt.jumia.services.brad.domain.entities.fake.FakeAccounts;
import pt.jumia.services.brad.domain.entities.fake.FakeCurrencies;
import pt.jumia.services.brad.domain.exceptions.DatabaseErrorsException;
import pt.jumia.services.brad.domain.exceptions.InvalidEntityException;
import pt.jumia.services.brad.domain.exceptions.NotFoundException;
import pt.jumia.services.brad.domain.repository.brad.BradBaleRepository;
import pt.jumia.services.brad.domain.usecases.bale.enrichment.BaleAccountEnricher;
import pt.jumia.services.brad.domain.usecases.bale.enrichment.BaleCurrencyResolver;
import pt.jumia.services.brad.domain.usecases.bale.enrichment.BaleFxRateEnricher;
import pt.jumia.services.brad.domain.usecases.bale.enrichment.CurrencyResolutionException;
import pt.jumia.services.brad.domain.usecases.bale.enrichment.FxRateUnavailableException;
import pt.jumia.services.brad.domain.usecases.bale.validation.BaleValidator;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.doNothing;

/**
 * Unit tests for SyncBradBaleUseCase focusing on simplified Spring Batch integration.
 * 
 * Tests cover:
 * - Individual bale processing (processBale method)
 * - Batch synchronization (execute method)
 * - Error handling and validation
 * - FX rate enrichment integration
 */
@ExtendWith(MockitoExtension.class)
public class SyncBradBaleUseCaseTest {

    private static final List<Bale> BALE_LIST = new ArrayList<>(FakeBale.getFakeBale(10));
    
    @Mock
    private BradBaleRepository bradBaleRepository;

    @Mock
    private BaleValidator baleValidator;

    @Mock
    private BaleAccountEnricher baleAccountEnricher;

    @Mock
    private BaleCurrencyResolver baleCurrencyResolver;

    @Mock
    private BaleFxRateEnricher baleFxRateEnricher;

    @InjectMocks
    private SyncBradBaleUseCase syncBradBaleUseCase;

    private List<Bale> testBales;
    private Account testAccount;
    private Currency testCurrency;

    @BeforeEach
    void setUp() {
        testAccount = FakeAccounts.getFakeAccounts(1, null).get(0);
        testCurrency = FakeCurrencies.ALL_CURRENCIES.get(0);
        testBales = FakeBale.getFakeBale(3);
    }

    // ========== TESTS FOR SIMPLIFIED EXECUTE METHOD ==========

    @Test
    public void execute_withValidBales_shouldSyncSuccessfully() throws DatabaseErrorsException {
        // Given
        when(bradBaleRepository.sync(anyList())).thenReturn(BALE_LIST);
        
        // When
        List<Bale> result = syncBradBaleUseCase.execute(BALE_LIST);

        // Then
        assertEquals(BALE_LIST, result);
        verify(bradBaleRepository).sync(BALE_LIST);
    }

    @Test
    public void execute_withEmptyList_shouldReturnEmptyList() throws DatabaseErrorsException {
        // Given
        List<Bale> emptyList = List.of();
        
        // When
        List<Bale> result = syncBradBaleUseCase.execute(emptyList);

        // Then
        assertTrue(result.isEmpty());
        verify(bradBaleRepository, never()).sync(any());
    }

    @Test
    public void execute_withNullList_shouldReturnEmptyList() throws DatabaseErrorsException {
        // When
        List<Bale> result = syncBradBaleUseCase.execute(null);

        // Then
        assertTrue(result.isEmpty());
        verify(bradBaleRepository, never()).sync(any());
    }

    @Test
    public void execute_withRepositoryException_shouldThrowDatabaseErrorsException() {
        // Given
        when(bradBaleRepository.sync(anyList())).thenThrow(new RuntimeException("Database error"));
        
        // When & Then
        DatabaseErrorsException exception = assertThrows(DatabaseErrorsException.class, 
            () -> syncBradBaleUseCase.execute(BALE_LIST));
        
        assertTrue(exception.getMessage().contains("Bale sync failed"));
    }

    // ========== TESTS FOR PROCESS BALE METHOD ==========

    @Test
    public void processBale_withValidBale_shouldEnrichSuccessfully() throws Exception {
        // Given
        Bale inputBale = testBales.get(0);
        Set<FxRate> mockFxRates = new HashSet<>();
        
        // Mock validator to not throw exceptions for valid bale
        doNothing().when(baleValidator).validateBale(any(Bale.class));
        when(baleAccountEnricher.fetchAccount(any(Bale.class))).thenReturn(testAccount);
        when(baleCurrencyResolver.determineCurrency(any(Bale.class), any(Account.class))).thenReturn(testCurrency);
        when(baleFxRateEnricher.enrichWithFxRates(any(Bale.class), any(Currency.class))).thenReturn(mockFxRates);

        // When
        Bale result = syncBradBaleUseCase.processBale(inputBale);

        // Then
        assertNotNull(result);
        assertEquals(testAccount, result.getAccount());
        assertEquals(testCurrency, result.getTransactionCurrency());
        assertNotNull(result.getFxRates());
        
        verify(baleValidator).validateBale(inputBale);
        verify(baleAccountEnricher).fetchAccount(inputBale);
        verify(baleCurrencyResolver).determineCurrency(inputBale, testAccount);
        verify(baleFxRateEnricher).enrichWithFxRates(inputBale, testCurrency);
    }

    @Test
    public void processBale_withNullBale_shouldThrowException() {
        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, 
            () -> syncBradBaleUseCase.processBale(null));
        
        assertEquals("Bale cannot be null", exception.getMessage());
    }

    @Test
    public void processBale_withInvalidBale_shouldThrowException() {
        // Given
        Bale invalidBale = Bale.builder().build(); // Missing required fields
        
        // Mock validator to throw exception for invalid bale
        doThrow(new InvalidEntityException("Invalid bale data"))
            .when(baleValidator).validateBale(invalidBale);
        
        // When & Then
        InvalidEntityException exception = assertThrows(InvalidEntityException.class, 
            () -> syncBradBaleUseCase.processBale(invalidBale));
        
        assertEquals("Invalid bale data", exception.getMessage());
        verify(baleValidator).validateBale(invalidBale);
    }

    @Test
    public void processBale_withAccountLookupFailure_shouldThrowException() {
        // Given
        Bale inputBale = testBales.get(0);
        
        // Mock validator to not throw exceptions so we can test account enricher
        doNothing().when(baleValidator).validateBale(any(Bale.class));
        when(baleAccountEnricher.fetchAccount(any(Bale.class)))
            .thenThrow(new NotFoundException("Account not found"));
        
        // When & Then
        NotFoundException exception = assertThrows(NotFoundException.class, 
            () -> syncBradBaleUseCase.processBale(inputBale));
        
        assertEquals("Account not found", exception.getMessage());
        verify(baleValidator).validateBale(inputBale);
        verify(baleAccountEnricher).fetchAccount(inputBale);
    }

    @Test
    public void processBale_withCurrencyLookupFailure_shouldThrowException() throws Exception {
        // Given
        Bale inputBale = testBales.get(0);
        
        // Mock validator to not throw exceptions so we can test currency resolver
        doNothing().when(baleValidator).validateBale(any(Bale.class));
        when(baleAccountEnricher.fetchAccount(any(Bale.class))).thenReturn(testAccount);
        when(baleCurrencyResolver.determineCurrency(any(Bale.class), any(Account.class)))
            .thenThrow(new CurrencyResolutionException("Currency not found"));
        
        // When & Then
        CurrencyResolutionException exception = assertThrows(CurrencyResolutionException.class, 
            () -> syncBradBaleUseCase.processBale(inputBale));
        
        assertEquals("Currency not found", exception.getMessage());
        verify(baleValidator).validateBale(inputBale);
        verify(baleAccountEnricher).fetchAccount(inputBale);
        verify(baleCurrencyResolver).determineCurrency(inputBale, testAccount);
    }

    @Test
    public void processBale_withFxRateEnrichment_shouldAddCorrectRates() throws Exception {
        // Given
        Bale inputBale = testBales.get(0);
        Set<FxRate> mockFxRates = new HashSet<>();
        
        // Mock validator to not throw exceptions so we can test FX rate enrichment
        doNothing().when(baleValidator).validateBale(any(Bale.class));
        when(baleAccountEnricher.fetchAccount(any(Bale.class))).thenReturn(testAccount);
        when(baleCurrencyResolver.determineCurrency(any(Bale.class), any(Account.class))).thenReturn(testCurrency);
        when(baleFxRateEnricher.enrichWithFxRates(any(Bale.class), any(Currency.class))).thenReturn(mockFxRates);
        
        // When
        Bale result = syncBradBaleUseCase.processBale(inputBale);

        // Then
        verify(baleValidator).validateBale(inputBale);
        verify(baleAccountEnricher).fetchAccount(inputBale);
        verify(baleCurrencyResolver).determineCurrency(inputBale, testAccount);
        verify(baleFxRateEnricher).enrichWithFxRates(inputBale, testCurrency);
        assertNotNull(result.getFxRates());
        assertEquals(mockFxRates, result.getFxRates());
    }
}
